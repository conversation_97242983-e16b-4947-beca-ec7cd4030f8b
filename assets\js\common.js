function ShowProgressAnimation() {
  $("#loading-div-background").show();
}

function HideProgressAnimation() {
  $("#loading-div-background").hide();
}

function SetPageTitle() {
  var title = document.getElementsByTagName("title")[0].innerHTML;
  title = title + " | " + applicationName;
  document.getElementsByTagName("title")[0].innerHTML = title;
}

function setCookie(cname, cvalue, exdays) {
  var d = new Date();
  d.setTime(d.getTime() + exdays * 24 * 60 * 60 * 1000);
  var expires = "expires=" + d.toUTCString();
  document.cookie = cname + "=" + cvalue + "; Path=/; " + expires;
}

function getCookie(cname) {
  var name = cname + "=";
  var ca = document.cookie.split(";");
  for (var i = 0; i < ca.length; i++) {
    var c = ca[i];
    while (c.charAt(0) == " ") c = c.substring(1);
    if (c.indexOf(name) == 0) return c.substring(name.length, c.length);
  }
  return "";
}

function deleteCookie(name) {
  document.cookie = name + "=; Path=/; Expires=Thu, 01 Jan 1970 00:00:01 GMT;";
}

// function CopyToClipboard(containerid) {
//     /* Get the text field */
//     var copyText = document.getElementById(containerid);

//     /* Select the text field */
//     copyText.select();
//     copyText.setSelectionRange(0, 99999); /* For mobile devices */

//     /* Copy the text inside the text field */
//     navigator.clipboard.writeText(copyText.value);

//     alertify.success('URL copied to clipboard');

// }

function CopyToClipboard(containerid) {
  var copyText = document.getElementById(containerid);

  // Check if the Clipboard API is supported
  if (navigator.clipboard) {
    navigator.clipboard
      .writeText(copyText.value)
      .then(function () {
        alertify.success("URL copied to clipboard");
      })
      .catch(function (error) {
        console.error("Unable to copy to clipboard: ", error);
      });
  } else {
    // Fallback for browsers that do not support the Clipboard API
    var textArea = document.createElement("textarea");
    textArea.value = copyText.value;
    document.body.appendChild(textArea);
    textArea.select();

    try {
      if (successful) alertify.success("URL copied to clipboard");
      else alertify.error("Unable to copy to clipboard");
    } catch (err) {
      console.error("Unable to copy to clipboard: ", err);
    } finally {
      document.body.removeChild(textArea);
    }
  }
}

$("input.validateOnlynumbers").keypress(function (event) {
  if (event.keyCode == 13) {
    return true;
  } else {
    return /\d/.test(String.fromCharCode(event.keyCode));
  }
});

// Copy Link Url to clipboard

function copyLinkUrl(eleObj) {
  // var preceptorId = $(eleObj).attr('preceptorId');
  // var preceptorNum = $(eleObj).attr('preceptorNum');
  // var evaluationId = $(eleObj).attr('evaluationId');
  // var rotationId = $(eleObj).attr('rotationId');
  // var evaluationType = $(eleObj).attr('evaluationType');
  var preceptorId = eleObj.hasAttribute("preceptorId")
    ? $(eleObj).attr("preceptorId")
    : 0;
  var preceptorNum = eleObj.hasAttribute("preceptorMobileNum")
    ? $(eleObj).attr("preceptorMobileNum")
    : "";
  var checkoffId = eleObj.hasAttribute("checkoffId")
    ? $(eleObj).attr("checkoffId")
    : 0;
  var schoolTopicId = eleObj.hasAttribute("schoolTopicId")
    ? $(eleObj).attr("schoolTopicId")
    : 0;
  var rotationId = eleObj.hasAttribute("rotationId")
    ? $(eleObj).attr("rotationId")
    : 0;
  var evaluationId = eleObj.hasAttribute("evaluationId")
    ? $(eleObj).attr("evaluationId")
    : 0;
  var email = eleObj.hasAttribute("email") ? $(eleObj).attr("email") : "";
  var evaluationType = eleObj.hasAttribute("evaluationType")
    ? $(eleObj).attr("evaluationType")
    : "";
  var preceptorNo = eleObj.hasAttribute("preceptorNum")
    ? $(eleObj).attr("preceptorNum")
    : "";
  var clinicianId = eleObj.hasAttribute("clinicianId")
    ? $(eleObj).attr("clinicianId")
    : 0;
  var SchoolId = eleObj.hasAttribute("SchoolId")
    ? $(eleObj).attr("SchoolId")
    : 0;
  var getClinicianCoarcId = eleObj.hasAttribute("getClinicianCoarcId")
    ? $(eleObj).attr("getClinicianCoarcId")
    : 0;
  var getStudentCoarcId = eleObj.hasAttribute("getStudentCoarcId")
    ? $(eleObj).attr("getStudentCoarcId")
    : 0;
  var coarcSurveyMasterId = eleObj.hasAttribute("coarcSurveyMasterId")
    ? $(eleObj).attr("coarcSurveyMasterId")
    : 0;
  var studentId = eleObj.hasAttribute("studentId")
    ? $(eleObj).attr("studentId")
    : 0;
  var irrDetailId = eleObj.hasAttribute("irrDetailId")
    ? $(eleObj).attr("irrDetailId")
    : 0;
  var irrMasterId = eleObj.hasAttribute("irrMasterId")
    ? $(eleObj).attr("irrMasterId")
    : 0;
  var activityId = eleObj.hasAttribute("activityId")
    ? $(eleObj).attr("activityId")
    : 0;
  var soapNoteId = eleObj.hasAttribute("soapNoteId")
    ? $(eleObj).attr("soapNoteId")
    : 0;

  // console.log('soapNoteId'+soapNoteId);
  // console.log('evaluationType'+evaluationType);
  // return false;
  preceptorNum =
    evaluationType == "checkoff" ||
    evaluationType == "floorTherapy" ||
    evaluationType == "activitySheet" ||
    evaluationType == "soapnote"
      ? preceptorNum
      : preceptorNo;

  $.ajax({
    type: "POST",
    url: "../ajax/ajax_copy_link.html",
    data: {
      preceptorId: preceptorId,
      preceptorNum: preceptorNum,
      evaluationId: evaluationId,
      rotationId: rotationId,
      evaluationType: evaluationType,
      checkoffId: checkoffId,
      schoolTopicId,
      schoolTopicId,
      clinicianId: clinicianId,
      SchoolId: SchoolId,
      getClinicianCoarcId: getClinicianCoarcId,
      getStudentCoarcId: getStudentCoarcId,
      coarcSurveyMasterId: coarcSurveyMasterId,
      studentId: studentId,
      irrdetailId: irrDetailId,
      irrMasterId: irrMasterId,
      activityId: activityId,
      soapNoteId: soapNoteId,
    },
    success: function (data) {
      var linkURL = data;
      if (linkURL != "") {
        // Display a prompt for manual copying
        var success = false;
        try {
          var textArea = document.createElement("textarea");
          textArea.value = linkURL;
          document.body.appendChild(textArea);
          textArea.select();
          success = document.execCommand("copy");
          document.body.removeChild(textArea);
        } catch (err) {
          console.error("Error copying to clipboard manually: ", err);
        }

        if (success) {
          alertify.success("URL copied to clipboard");
        } else {
          alertify.prompt(
            "Copy URL",
            "Note: You can manually copy the URL.",
            linkURL,
            function (evt, value) {
              // alertify.success('You entered: ' + value);
            },
            function () {
              // alertify.error('Cancel');
            }
          );
        }
      }
    },
  });
}

// $(document).ready(function(){
// Add an event listener for when a panel is shown
$(".panel-collapse").on("shown.bs.collapse", function () {
  // Get the ID of the panel that was just shown
  var panelId = $(this).attr("id");
  // Calculate the position of the panel with a 50px space from the top
  var position = $("#" + panelId).offset().top - 55;

  // Scroll to the panel using a smooth animation
  $("html, body").animate(
    {
      scrollTop: position,
    },
    500
  ); // Adjust the duration (in milliseconds) as needed
});
//   });

// scroll top top for case study

$(document).ready(function () {
  // Assuming your collapsible elements have a class named "collapsible"
  $(".collapsible").on("click", function () {
    // Check if the clicked element has the "active" class
    if ($(".content").hasClass("active")) {
      //   console.log(">>>>>>>>>");
      // Get the position you want to scroll to
      var position = $(this).offset().top - 10; /* specify your position here */

      // Use animate to scroll to the specified position
      $("html, body").animate(
        {
          scrollTop: position,
        },
        500
      );
    }
  });

  // Assuming your next button has a class named '.next'
  $(".next").on("click", function () {
    scrollToTop();
  });

  // Assuming your previous button has a class named '.button-prev'
  $(".button-prev").on("click", function () {
    scrollToTop();
  });

  // Function to scroll to the top of the page
  function scrollToTop() {
    $("html, body").animate(
      {
        scrollTop: 0,
      },
      500
    );
  }
});

//   Validate the phone no and student record id is exist or not
function validateData(type, schoolId, studentId) {
  return new Promise(function (resolve, reject) {
    if (type == "recordId") var value = $("#recordIdNumber").val();
    else var value = $("#txtPhone").val();
    // var schoolId = '<?php echo $schoolId; ?>';
    // var studentId = '<?php echo ($studentId); ?>';
    $.ajax({
      url: "../ajax/ajax_validate_student.html",
      type: "POST",
      data: {
        value: value,
        type: type,
        schoolId: schoolId,
        studentId: studentId,
      },
      dataType: "json",
      success: function (response) {
        // console.log(response);
        var status = response["status"];
        var userId = response["studentId"];

        if (status == 1 && studentId != userId) {
          if (type == "recordId") {
            alertify.error("Student ID is already exist");
            // if (studentId == 0)
            $("#recordIdNumber").val("");

            resolve(false);
          } else {
            alertify.error("Phone number is already exist");
            if (studentId == 0) $("#txtPhone").val("");
            resolve(false);
          }
        } else {
          resolve(true);
        }
      },
      error: function (xhr) {
        // Handle error
        console.log(xhr.responseText);
        reject(xhr.responseText);
      },
    });
  });
}

function numberOnly(id) {
  var element = document.getElementById(id);
  element.value = element.value.replace(/[^0-9]/gi, "");
}

// Loop through each select element with the class 'yourClass'
$(document).ready(function () {
  // List of IDs to which sorting should be applied
  const allowedIds = [
    "cborotation",
    "cboRotation",
    "rotation",
    "rotationlist",
    "mainrotation",
  ];

  $(".select2_single").each(function () {
    var $select = $(this);

    // Apply sorting only if the ID is in the allowed list and the select is not disabled
    if (!allowedIds.includes($select.attr("id")) || $select.is(":disabled")) {
      return; // Skip this dropdown
    }

    // Get the current selected value to keep it unchanged
    var selectedValue = $select.val();

    // Reorder the options as needed
    var options = $select.find("option").sort(function (a, b) {
      // Place the option with empty value at the start
      if ($(a).val() === "") return -1; // Empty value comes first
      if ($(b).val() === "") return 1; // Empty value comes first

      // Custom sorting logic (alphabetical order for non-empty values)
      return $(a).text().localeCompare($(b).text());
    });

    // Empty the select element and append the sorted options
    $select.empty().append(options);

    // Reinitialize Select2 after sorting the options
    $select.select2();

    // Restore the selected value without triggering change event
    $select.val(selectedValue);
  });
});

//Chat Authenticate user
let newWindow = null;
function chatAuth(
  conversationId,
  authorRole,
  loggedBusinessId,
  loggedUserId,
  roleId,
  loggedUserEmail
) {
  // console.log('conversationId',conversationId);
  // console.log('authorRole',authorRole);
  // console.log('loggedBusinessId',loggedBusinessId);
  // console.log('loggedUserId',loggedUserId);
  // console.log('roleId',roleId);return false;

  // var userconversationId = (conversationId > 0) ? conversationId : 0;

  // var loggedBusinessId = $('#loggedBusinessId').val();
  // var loggedUserId = $('#loggedEmployeeId').val();
  // var isPrimary = $('#isPrimary').val();
  // var roleId = (isPrimary == 1) ? 2 : 3;

  $.ajax({
    type: "GET",
    url: "../ajax/ajax_authenticat_chat_user.html",
    data: {
      loggedBusinessId: loggedBusinessId,
      loggedUserId: loggedUserId,
      roleId: roleId,
      userconversationId: conversationId,
      authorRole: authorRole,
    },
    // dataType: "json",
    success: function (data) {
      console.log(data);

      // alert(data);
      if (isValidURL(data)) {
        $(".custom-popover").css("display", "none");
        $("#backdrop").css("display", "none");
        console.log("Valid URL");
        if (newWindow && newWindow.window !== null) {
          console.log("if");
          newWindow.focus();
        } else {
          console.log("else");
          // newWindow = window.open(data, "_blank");
          console.log("Opening new Chat APP window.");
          newWindow = window.open(data, "chatAppWindow");

          if (newWindow) {
            newWindow.name = "chatAppWindow"; // Set unique name

            // Store window details in localStorage
            localStorage.setItem(
              "chatAppWindow",
              JSON.stringify({
                url: data,
                windowName: newWindow.name,
                isOpen: true,
              })
            );
          }
        }

        setTimeout(() => {
          // console.log('inside here');
          GetChatNotificationsList(
            roleId,
            loggedUserEmail,
            loggedBusinessId,
            loggedUserId
          );
        }, 5000);

        console.log("New Window ", newWindow);
      } else {
        alertify.error(data);
      }
    },
  });
}

function isValidURL(string) {
  try {
    new URL(string);
    return true;
  } catch (_) {
    return false;
  }
}

// Firebase Notification
function SendDeviceTokenToChatApp(
  currentToken,
  schoolId,
  role,
  loggedUserEmail,
  ref_userId,
  xpikey,
  domain
) {
  // console.log('currentToken', currentToken);
  var schoolId = schoolId;
  var role = role;
  var loggedUserEmail = loggedUserEmail;
  var ref_userId = ref_userId;
  var xpikey = xpikey;

  $.ajax({
    type: "POST",
    url: "../ajax/ajax_send_device_token.html",
    data: {
      schoolId: schoolId,
      ref_userId: ref_userId,
      email: loggedUserEmail,
      role: role,
      fcmToken: currentToken,
    },
    dataType: "json",
    success: function (data) {
      console.log(data);
    },
  });
}

function GetChatNotificationsList(
  role,
  loggedUserEmail,
  loggedBusinessId,
  loggedUserId
) {
  var role = role;
  var loggedUserEmail = loggedUserEmail;
  var loggedBusinessId = loggedBusinessId;
  var loggedUserId = loggedUserId;

  $.ajax({
    type: "GET",
    url: "../ajax/ajax_get_chat_notifications.html",
    data: {
      loggedUserEmail: loggedUserEmail,
      role: role,
    },
    dataType: "json",
    success: function (response) {
      // console.log(response);
      if (response.response.statusCode == 200) {
        // console.log(notificationCount);
        var notificationCount = response.response.data.count;

        // console.log("notificationCount :", notificationCount);

        var webNotificationCount = $("#webNotiCount").text();
        webNotificationCount =
          webNotificationCount == "" ? 0 : webNotificationCount;
        // console.log("webNotificationCount :", webNotificationCount);

        var totalnotiCount =
          parseInt(webNotificationCount) + parseInt(notificationCount);

        // console.log("totalnotiCount :", totalnotiCount);

        $("#notification-count").text(totalnotiCount);

        $("#countValue2").text(notificationCount);

        $("#chatApp").empty();

        // Group notifications by _id
        var groupedNotifications = {};
        response.response.data.forEach(function (notification) {
          if (!groupedNotifications[notification._id]) {
            groupedNotifications[notification._id] = {
              authorId: notification.authorId,
              authorFirstName: notification.authorFirstName,
              authorLastName: notification.authorLastName,
              authorRole: notification.authorRole,
              conversationId: notification.conversationId,
              authorProfilePic: notification.authorProfilePic
                ? notification.authorProfilePic
                : "https://staging.clinicaltrac.net/upload/default-user.png",
              notifications: [],
            };
          }
          groupedNotifications[notification._id].notifications.push(
            notification
          );
        });

        // Loop through grouped notifications
        for (var authorId in groupedNotifications) {
          if (groupedNotifications.hasOwnProperty(authorId)) {
            var author = groupedNotifications[authorId];
            var conversationId = author.conversationId;
            var authorRole = author.authorRole;
            // Create HTML for author section
            var authorHtml = `<a href="javascript:void(0);" onclick="chatAuth('${conversationId}','${authorRole}','${loggedBusinessId}','${loggedUserId}','${role}','${loggedUserEmail}')" class="viewNotificationPopup">
                <div class="author-section">
                    <ul class="notification-list" style="overflow: auto;height: auto;">`;

            // Loop through notifications for this author
            author.notifications.forEach(function (notification) {
              // Get the timestamp for the current notification
              var timestamp = notification.updatedAt;
              // console.log("timestamp "+timestamp);
              // Calculate the time ago for this notification
              var time = getTimeAgo(timestamp);

              // Create HTML for each notification
              authorHtml += `<li>
                    <div class="notification" style="display: flex;align-items: center;">
                        <div class="notification-avatar">
                            <img src="${author.authorProfilePic}" alt="${author.authorFirstName}'s avatar">
                        </div>
                        <div class="notification-details">
                            <h4>${author.authorFirstName} ${author.authorLastName}</h4>
                            <h6 class="notification-heading">You have ${notification.count} new messages</h6>
                            <p class="notification-text">${time}</p>
                        </div>
                    </div>
                    <hr>
                </li>`;
            });

            // Close author section HTML
            authorHtml += `</ul></div></a>`;

            // Append author section HTML to chatNotification element
            $("#chatApp").append(authorHtml);
          }
        }
      } else {
        $("#notification-count").text("");
        $("#countValue2").addClass("hide");
        $("#chatApp").empty();

        $("#chatApp").append("<p>No notifications in Chat.</p>");

        // var probizcaNotificationCount = '<?php // echo ($notificationCount); ?>';
        // var totalnotiCount = parseInt(probizcaNotificationCount) + parseInt(0);
        // $('#countValue').text(totalnotiCount);
        $("#countValue2").text("");
      }
    },
    error: function (xhr, status, error) {
      // Handle errors
      console.log(xhr.responseText);
    },
  });
}

function getTimeAgo(timestamp) {
  const date = new Date(timestamp);
  const currentDate = new Date();
  const differenceMs = currentDate - date;
  const secondsAgo = Math.floor(differenceMs / 1000);
  const minutesAgo = Math.floor(secondsAgo / 60);
  const hoursAgo = Math.floor(minutesAgo / 60);
  const daysAgo = Math.floor(hoursAgo / 24);
  const monthsAgo = Math.floor(daysAgo / 30);
  const yearsAgo = Math.floor(daysAgo / 365);

  if (yearsAgo > 0) {
    return yearsAgo === 1 ? "1 year ago" : `${yearsAgo} years ago`;
  } else if (monthsAgo > 0) {
    return monthsAgo === 1 ? "1 month ago" : `${monthsAgo} months ago`;
  } else if (daysAgo > 0) {
    return daysAgo === 1 ? "1 day ago" : `${daysAgo} days ago`;
  } else if (hoursAgo > 0) {
    return hoursAgo === 1 ? "1 hour ago" : `${hoursAgo} hours ago`;
  } else if (minutesAgo > 0) {
    return minutesAgo === 1 ? "1 minute ago" : `${minutesAgo} minutes ago`;
  } else {
    return "just now";
  }
}

/* Notofication Custom Toast */
toast = document.querySelector(".custom-toast");
closeIcon = document.querySelector(".toast-close-icon");
progress = document.querySelector(".chat-progress");

let timer1, timer2;

closeIcon.addEventListener("click", () => {
  toast.classList.remove("active");
  console.log("outside here");
  setTimeout(() => {
    console.log("inside here");
    progress.classList.remove("active");
  }, 0);

  clearTimeout(timer1);
  clearTimeout(timer2);
});
/* Notofication Custom Toast End */

/* WebSocket Connection Start*/

$(document).on("click", "#logout", function () {});

let socket;
let isConnected = false;
function WebSocketConnectionStart(
  loggedBusinessId,
  loggedUserEmail,
  loggedUserId,
  role
) {
  /* WebSocket Connection Start*/
  // console.log(sessionStorage.getItem('socketConnected'));

  $.ajax({
    type: "POST",
    url: "../ajax/ajax_get_user_chat_access_token.html",
    data: {
      loggedBusinessId: loggedBusinessId,
      LoggedUserEmail: loggedUserEmail,
      loggedUserId: loggedUserId,
      roleId: role,
      isOnline: 1,
    },
    dataType: "json",
    success: function (data) {
      console.log("Chat data ", data);
    },
  });
}

$("#logoutButton").on("click", function () {
  closeAuditLogWindow();
  // Ensure the audit log window closes before redirecting
  setTimeout(function () {
    console.log("Redirecting to logout page.");
    window.location.href = "./logout.html";
  }, 300); // Delay of 300ms to ensure proper closure
});

/**
 * AuthorizeAuditLog
 *
 * @description Authenticate user to access audit log
 *
 * @param {int} schoolId - The ID of the school
 *
 * @returns {undefined}
 */
let auditlogwindow = null;
function AuthorizeAuditLog(schoolId) {
  $("#audit-log-icon").hide();
  $("#audit-log-spinner").show();
  $.ajax({
    type: "GET",
    url: "../ajax/ajax_authenticat_audit_log.html",
    data: {
      schoolId: schoolId,
    },
    // dataType: "json",
    success: function (data) {
      // console.log(data);
      $("#audit-log-icon").show();
      $("#audit-log-spinner").hide();

      if (isValidURL(data)) {
        if (auditlogwindow && !auditlogwindow.closed) {
          // console.log("Focusing on existing window.");
          auditlogwindow.focus();
        } else {
          console.log("Opening new audit log window.");
          auditlogwindow = window.open(data, "auditLogWindow");

          if (auditlogwindow) {
            auditlogwindow.name = "auditLogWindow"; // Set unique name

            // Store window details in localStorage
            localStorage.setItem(
              "auditLogWindow",
              JSON.stringify({
                url: data,
                windowName: auditlogwindow.name,
                isOpen: true,
              })
            );
          }
        }
      } else {
        alertify.error(data);
      }
    },
  });
}

// Function to close the audit log window on logout
function closeAuditLogWindow() {
  const storedData = localStorage.getItem("auditLogWindow");
  console.log("storedData:", storedData);

  if (storedData) {
    const { windowName, isOpen } = JSON.parse(storedData);

    if (isOpen) {
      console.log("Trying to close audit log window:", windowName);

      // Find the existing audit log window
      auditlogwindow = window.open("", windowName); // Try to get existing window

      if (auditlogwindow && !auditlogwindow.closed) {
        console.log("Closing existing audit log window.");
        auditlogwindow.close();
      }

      // Remove from localStorage
      localStorage.removeItem("auditLogWindow");
    }
  }
}

// Save section wise case study data
function saveCaseStudyDetails(sectionTitle, step, onSuccessCallback) {
    var stepData = {};
    var caseStudyId = $('#hiddenCaseStudyId').val();
    var studentId = $('#userId').val(); // userId refers to StudentId.
    var type = $('#hiddenType').val();
    var IsMobile = $('#IsMobile').val();

    stepData['caseStudyId'] = caseStudyId;
    stepData['studentId'] = studentId;
    stepData['sectionTitle'] = sectionTitle;
    stepData['type'] = type;
    stepData['IsMobile'] = IsMobile;

    $('#step-' + step).find('input, select, textarea').each(function () {
        var name = $(this).attr('name');
        var inputType = $(this).attr('type');
        var value;

        if (inputType === 'checkbox') {
            value = $(this).is(':checked') ? $(this).val() : '';
        } else if (inputType === 'radio') {
            if (!$(this).is(':checked')) return; // skip unchecked radios
            value = $(this).val();
        } else {
            value = $(this).val();
        }

        if (name) {
            stepData[name] = value;
        }
    });


    console.log(stepData); // This will be an array of checked values

    if (type == 'PACR' && (sectionTitle == 'cbc' || sectionTitle == 'medications')) {
        var checkedCBCValues = [];

        $('input[name="pacrCBCCheckValue[]"]:checked').each(function () {
            checkedCBCValues.push($(this).val());
            // Sort the values in ascending order
            checkedCBCValues.sort();
        });
        stepData['pacrCBCCheckValues'] = checkedCBCValues;

    }
    if (sectionTitle == 'comments') {
        $('#btnSave').html(loadingText());
        $('#btnSave').prop('disabled', true);
    } else {
        $('.next').html(loadingText());
        $('.next').prop('disabled', true);
    }
    $.ajax({
        url: '../ajax/ajax_save_case_study.html',
        method: 'POST',
        data: stepData,
        dataType: 'json',
        success: function (response) {


            if (sectionTitle == 'comments') {

                $('#btnSave').html('Submit');
                $('#btnSave').prop('disabled', false);
            } else {
                $('.next').html('Save & Next');
                $('.next').prop('disabled', false);

            }
            // console.log(response);
            if (response.status === 'Success') {
                $('#hiddenCaseStudyId').val(response.caseStudyId);
                if (response.url != '') {
                    alertify.success('Section saved successfully.');
                    window.location.href = response.url;
                }else{
                alertify.success('Section saved successfully. Proceeding to the next section.');

                }
                // ✅ Call the passed callback function
                if (typeof onSuccessCallback === 'function') {
                    onSuccessCallback();
                }
            } else {
                alertify.error('Error Occured.');
                if (sectionTitle == 'comments') {

                $('#btnSave').html('Submit');
                $('#btnSave').prop('disabled', false);
            } else {
                $('.next').html('Save & Next');
                $('.next').prop('disabled', false);

            }
            }
        },
        error: function (xhr, status, error) {
            alertify.error('Error Occured.');
        }
    });
}

// ===================== COMMON AUTOSAVE & DRAFTS =====================

/**
 * Checks if at least one field in the form has a non-empty value (excluding hidden fields).
 * @param {HTMLElement|jQuery} form - The form element or jQuery object.
 * @returns {boolean}
 */
function isFormDataNotEmpty(form) {
    var notEmpty = false;
    $(form).find('input[type="text"], textarea, select').each(function () {
        if ($(this).val() && $(this).val().toString().trim() !== '') {
            notEmpty = true;
            return false; // break loop
        }
    });
    return notEmpty;
}

/**
 * Auto-save form data via AJAX. Updates draftId fields and refreshes drafts list if provided.
 * @param {Object} options - { formSelector, saveUrl, draftsListCallback, draftIdField, hiddenDraftIdField, statusSelector }
 */
function autoSaveDraft(options) {
    var draftLimit = $('#draftLimit').val();
    console.log("moduleLimit ",draftLimit);
    var form = document.querySelector(options.formSelector);
    if (!isFormDataNotEmpty(form)) return;
    var formData = new FormData(form);

    // Add select2 displayed text for autosave
    $(form).find('select.select2_single, select.select2_multiple').each(function() {
        var name = $(this).attr('name') || $(this).attr('id');
        var text = $(this).find('option:selected').map(function() { return $(this).text(); }).get().join(', ');
        if (name) {
            formData.append(name + '_text', text);
        }
    });

    // Add draftLimit to formData
    if (draftLimit) {
        formData.append('draftLimit', draftLimit);
    }
    $.ajax({
        url: options.saveUrl,
        type: 'POST', 
        data: formData,
        processData: false,
        contentType: false,
        dataType: 'json',
        success: function (response) {
            if (options.statusSelector) {
                $(options.statusSelector).text(response.message || response);
            }
            if (typeof options.draftsListCallback === 'function') {
                options.draftsListCallback();
            }
            // Update draftId fields
            if (response.draftId) {
                if ($(options.draftIdField).length === 0) {
                    $('<input>').attr({ type: 'hidden', id: options.draftIdField.replace('#',''), name: 'draftId', value: response.draftId }).appendTo(options.formSelector);
                } else {
                    $(options.draftIdField).val(response.draftId);
                }
                if (options.hiddenDraftIdField) {
                    $(options.hiddenDraftIdField).val(response.draftId);
                }

                $('#floatingSaveButton').removeClass('hide');
                $('#floatingButton').addClass('hide');
                setTimeout(function() {
                    $('#floatingSaveButton').addClass('hide');
                    $('#floatingButton').removeClass('hide');
                }, 2000);
            }
        },
        error: function (xhr, status, error) {
            // console.error('AutoSave failed:', error);
        }
    });
}

/**
 * Loads the list of drafts for a user and injects HTML into a container.
 * @param {Object} options - { listUrl, postData, containerSelector, onSelectDraft }
 */
function loadDraftsList(options) {
    $.ajax({
        url: options.listUrl,
        method: 'POST',
        data: options.postData,
        dataType: 'json',
        success: function (drafts) {
            console.log(drafts);
            var html = '';

            if (!drafts || drafts.length === 0) {
                html = '<div class="text-muted">No drafts found.</div>';
            } else {
                html += `<ul class="list-group">`;

                drafts.forEach(function (draft, idx) {
                    console.log(draft.moduleName);  
                   
                    // Use cborotation_text for display name, fallback to Draft-N if missing or empty
                    var displayName = '';
                    if (draft.cborotation_text !== undefined && draft.cborotation_text !== null) {
                        displayName = String(draft.cborotation_text).trim();
                    }
                    if (!displayName) {
                        displayName = `Draft-${idx + 1}`;
                    }
                    // Format createdDateTime as mm/dd/yy
                    var createdDate = '';
                    if (draft.createdDateTime) {
                        var d = new Date(draft.createdDateTime.replace(/-/g, '/'));
                        var mm = String(d.getMonth() + 1).padStart(2, '0');
                        var dd = String(d.getDate()).padStart(2, '0');
                        var yy = String(d.getFullYear()).slice(-2);
                        createdDate = `${mm}/${dd}/${yy}`;
                    }
                    html += `
                <li class="list-group-item" style="padding: 10px;border-radius: 10px;">
    <a class="draft-title load-draft" title="Edit" data-file="${draft.file}" data-draftid="${draft.draftId}">
        ${displayName}
    </a>
    <div class="autosave-date">
        <small>${createdDate}</small>
    </div>
    <div class="draft-actions">
        <button class="btn-delete" title="Delete" data-draftid="${draft.draftId}">
            <i class="glyphicon glyphicon-trash"></i>
        </button>
    </div>
</li>
`;
                });

                html += `</ul>`;
            }

            $(options.containerSelector).html(html);

            // Bind click handler for selecting a draft
            if (typeof options.onSelectDraft === 'function') {
                $(options.containerSelector).off('click', '.load-draft').on('click', '.load-draft', function (e) {
                    e.preventDefault();
                    options.onSelectDraft(this);
                });
            }

            // Toggle edit icon (optional visual toggle)
            $(options.containerSelector).off('click', '.btn-edit-toggle').on('click', '.btn-edit-toggle', function (e) {
                e.preventDefault();
                var $icon = $(this).find('i');
                if ($icon.hasClass('glyphicon-edit')) {
                    $icon.removeClass('glyphicon-edit').addClass('glyphicon-remove');
                } else {
                    $icon.removeClass('glyphicon-remove').addClass('glyphicon-edit');
                }
            });

            // Bind delete draft
            $(options.containerSelector).off('click', '.btn-delete').on('click', '.btn-delete', function (e) {
                e.preventDefault();
                var draftId = $(this).data('draftid');
                   var userId = $('#userId').val();
                   var currentSchoolId = $('#currentSchoolId').val();
                   var moduleName = $('#moduleName').val();
                // AJAX call to delete the draft
                $.ajax({
                    url: '../../../ajax/ajax_delete_autoSaveDraft.html',
                    method: 'POST',
                    data: { draftId: draftId,userId:userId,currentSchoolId:currentSchoolId,moduleName:moduleName },
                    dataType: 'json',
                    success: function(response) {
                        if (response.success) {
                            // Optionally show a success message
                            alertify.success('Draft deleted successfully.');
                            // If the deleted draft is currently loaded, clear the form fields and draftId fields
                            var currentDraftId = $('#draftId').val();
                            if (!currentDraftId || String(currentDraftId) === String(draftId)) {
                                // Try to clear the form by id, name, or fallback to all forms
                                var $form = options.formSelector ? $(options.formSelector) : $('form');
                                $form.find('input[type="text"], input[type="number"], input[type="email"], input[type="password"], textarea, select').val('');
                                $form.find('input[type="checkbox"], input[type="radio"]').prop('checked', false);
                                $form.find('#draftId, #hiddendraftId').val('');
                                $form.find('select').val('').trigger('change'); 
                            }
                            // Reload the drafts list
                            if (typeof options.draftsListCallback === 'function') {
                                options.draftsListCallback();
                            } else {
                                // fallback: reload the list
                                loadDraftsList(options);
                            }
                        } else {
                            alertify.error(response.message || 'Failed to delete draft.');
                        }
                    },
                    error: function(xhr, status, error) {
                        alertify.error('Failed to delete draft.');
                    }
                });
            });
        },
        error: function () {
            $(options.containerSelector).html('<div class="text-danger">Failed to load drafts.</div>');
        }
    });
}



// --- Load drafts list using common.js ---
function loadDrafts() {
    loadDraftsList({
        listUrl: '../../../ajax/ajax_list_autosave_drafts.html',
        postData: {
            userId: $('#userId').val(),
            currentSchoolId: $('#currentSchoolId').val(),
            moduleName: $('#moduleName').val()
        },
        containerSelector: '#draftsList',
        onSelectDraft: function (el) {
            var userId = $('#userId').val();
            var currentSchoolId = $('#currentSchoolId').val();
            var moduleName = $('#moduleName').val();
            
            var file = $(el).data('file');
            var draftId = $(el).data('draftid');
            $('#hiddendraftId').val(draftId);
            if ($('#draftId').length === 0) {
                $('<input>').attr({ type: 'hidden', id: 'draftId', name: 'draftId', value: draftId }).appendTo('#frmintraction');
            } else {
                $('#draftId').val(draftId);
            }
            loadDataIntoForm(currentSchoolId,userId,moduleName,file);
        }
    });
}


