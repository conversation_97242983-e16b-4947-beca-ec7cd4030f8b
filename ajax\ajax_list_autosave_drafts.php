<?php

// echo "<pre>";
// print_r($_POST);
// exit;
// This script returns a list of draft files for the logged-in student in JSON format
$userId = $_POST['userId'] ?? 0;
$currentSchoolId = $_POST['currentSchoolId'] ?? 0;
$moduleName = $_POST['moduleName'] ?? '';

// Validate required params 
if (!$userId || !$currentSchoolId || !$moduleName) {
    header('Content-Type: application/json');
    echo json_encode([]);
    exit;
}

// Use DIRECTORY_SEPARATOR for cross-platform compatibility
$baseDir = dirname(__DIR__); // goes up from /clinicaltrac/student to /clinicaltrac
$draftsDir = $baseDir . DIRECTORY_SEPARATOR . "upload" . DIRECTORY_SEPARATOR . "schools" . DIRECTORY_SEPARATOR . $currentSchoolId . DIRECTORY_SEPARATOR . "student" . DIRECTORY_SEPARATOR . $userId . DIRECTORY_SEPARATOR . "autosave" . DIRECTORY_SEPARATOR . $moduleName; 

// echo $draftsDir;exit;
$result = [];
if (is_dir($draftsDir)) {
    // Ensure the directory path ends with a separator
    $draftsDir = rtrim($draftsDir, DIRECTORY_SEPARATOR) . DIRECTORY_SEPARATOR;
    $pattern = $draftsDir . "user_{$userId}_draft_*.json";
    $files = glob($pattern);
    // Debug: log the pattern and files found
    // file_put_contents(__DIR__ . '/debug_list_drafts.log', print_r(['pattern' => $pattern, 'files' => $files], true), FILE_APPEND);
    foreach ($files as $file) {
        $data = json_decode(file_get_contents($file), true);
        if ($data) {
            $result[] = [
                'file' => basename($file),
                'moduleName' => ($moduleName),
                'draftId' => $data['draftId'] ?? '',
                'createdDateTime' => $data['createdDateTime'] ?? '',
                'updatedDateTime' => $data['updatedDateTime'] ?? '',
                'summary' => $data['interactionSummary'] ?? '',
                'cborotation_text' => isset($data['cborotation_text']) ? ($data['cborotation_text']) : '',
                'cborotation' => isset($data['cborotation']) ? ($data['cborotation']) : '',
            ];
        }
    }
    // Sort drafts by updatedDateTime descending (most recent first)
    usort($result, function($a, $b) {
        return strtotime($b['updatedDateTime']) <=> strtotime($a['updatedDateTime']);
    });
}
header('Content-Type: application/json');
echo json_encode($result);
