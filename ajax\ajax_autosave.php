<?php
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
// echo "<pre>";
// print_r($_POST);
// exit;

$currentSchoolId  = ($_POST['currentSchoolId']) ?? 0;
$hiddendraftId  = ($_POST['draftId']) ?? 0;
$draftLimit  = ($_POST['draftLimit']) ?? 0;
$type  = ($_POST['moduleName']) ?? 0;


if($type == 'interaction')
{
    $userId  = ($_POST['loggedStudentId']) ?? 0;

    $interactionDate = date('m/d/Y', strtotime($_POST['interactionDate']));

    $clinicianId  = ($_POST['cboClinicalinstructor']);
    $timeSpent  = ($_POST['txtTimeSpent']);
    // $interactionType  = ($_POST['interactionType']);

    $pointsAwarded  = ($_POST['txtPointsAwarded']);
    $interactionSummary  = ($_POST['Student_Response']);
    $hospitalsiteunits  = ($_POST['cbohospitalsiteunits']);
    $rotationId  = isset($_POST['cborotation']) ? ($_POST['cborotation']) : 0;
  

    // Prepare data array
    $data = [
        'draftId' => null, // will be set in saveDraftFile
        'schoolId' => $currentSchoolId,
        'studentId' => $userId ,
        'rotationId' => $rotationId,
        'clinicianId' => $clinicianId,
        'timeSpent' => $timeSpent,
        'pointsAwarded' => $pointsAwarded,
        'interactionSummary' => $interactionSummary,
        'hospitalSiteUnitId' => $hospitalsiteunits,
        'interactionDate' => $interactionDate,
        'createdBy' => $userId,
        'createdDateTime' => null, // will be set in saveDraftFile
        'updatedDateTime' => null, // will be set in saveDraftFile
    ];

} 
else if($type == 'pacr') {
    // print_r($_POST);
    $userId  = ($_POST['hiddenStudentId']) ?? 0;

    $caseStudydate = date('m/d/Y', strtotime($_POST['caseStudydate']));
    $pacrAdmissionDate = date('m/d/Y', strtotime($_POST['pacrAdmissionDate']));
    $pacrCBGDate = date('m/d/Y', strtotime($_POST['pacrCBGDate']));

    $pacrCBCCheckValue = ($_POST['pacrCBCCheckValue'] ?? []);
    $pacrMedications = ($_POST['pacrMedications'] ?? []);
    $pacrClassifications = ($_POST['pacrClassifications'] ?? []);
    $pacrModeOfctions = ($_POST['pacrModeOfctions'] ?? []);
    $pacrDosage = ($_POST['pacrDosage'] ?? []);

    // Debug: print the incoming medications array
    // print_r($pacrMedications);    
    $pacrMedicationsKeyed = [];
    $cnt1 = 0;
    foreach ($pacrMedications as $idx => $val) {
        // Always assign, even if empty, so pacrMedications1 is always set
        $pacrMedicationsKeyed['pacrMedications' . ($cnt1 + 1)] = is_array($val) ? ($val[0] ?? '') : $val;
        $cnt1 ++;
    } 

    $pacrClassificationsKeyed = []; 
    $cnt2 = 0;
    foreach ($pacrClassifications as $idx => $val) {
        // Always assign, even if empty, so pacrMedications1 is always set
        $pacrClassificationsKeyed['pacrClassifications' . ($cnt2 + 1)] = is_array($val) ? ($val[0] ?? '') : $val;
        $cnt2 ++;
    }

    $pacrModeOfctionsKeyed = [];  
    $cnt3 = 0;
    foreach ($pacrModeOfctions as $idx => $val) {
        // Always assign, even if empty, so pacrMedications1 is always set
        $pacrModeOfctionsKeyed['pacrModeOfctions' . ($cnt3 + 1)] = is_array($val) ? ($val[0] ?? '') : $val;
        $cnt3 ++;
    }

    $pacrDosageKeyed = [];   
    $cnt4 = 0;
    foreach ($pacrDosage as $idx => $val) {
        // Always assign, even if empty, so pacrMedications1 is always set
        $pacrDosageKeyed['pacrDosage' . ($cnt4 + 1)] = is_array($val) ? ($val[0] ?? '') : $val;
        $cnt4 ++;
    }

    $pacrCBCCheckValueKeyed = [];   
    $cnt5 = 0;
    foreach ($pacrCBCCheckValue as $idx => $val) {
        // Always assign, even if empty, so pacrMedications1 is always set
        $pacrCBCCheckValueKeyed['pacrCBCCheckValue' . ($cnt5 + 1)] = is_array($val) ? ($val[0] ?? '') : $val;
        $cnt5 ++;
    }


    $data = [
        'hiddenCaseStudyId' => ($_POST['hiddenCaseStudyId'] ?? 0),
        'caseStudydate' => ($caseStudydate ?? ''),
        'cborotation' => ($_POST['cborotation'] ?? ''),
        'cborotation_text' => isset($_POST['cborotation_text']) ? trim($_POST['cborotation_text']) : '',
        'HospitalSite' => $_POST['cbohospitalsites'] ?? 0,
        'cbohospitalsites_text' => isset($_POST['cbohospitalsites_text']) ? trim($_POST['cbohospitalsites_text']) : '',
        'cbohospitalsiteunits' => $_POST['cbohospitalsiteunits'] ?? 0,
        'cbohospitalsiteunits_text' => isset($_POST['cbohospitalsiteunits_text']) ? trim($_POST['cbohospitalsiteunits_text']) : '',
        'pacrCohort' => $_POST['pacrCohort'] ?? '',
        'pacrWeek' => $_POST['pacrWeek'] ?? '',

        'pacrAdmissionDate' => ($pacrAdmissionDate ?? ''),
        'pacrPtAge' => $_POST['pacrPtAge'] ?? '',
        'pacrSex' => $_POST['pacrSex'] ?? '',
        'pacrCC' => $_POST['pacrCC'] ?? '',
        'pacrDx' => $_POST['pacrDx'] ?? '',
        'pacrHx' => $_POST['pacrHx'] ?? '',
        'pacrPhysicalExam' => $_POST['pacrPhysicalExam'] ?? '',
        'pacrBreathSounds' => $_POST['pacrBreathSounds'] ?? '',

        'pacrTime1' => ($_POST['pacrTime1'] ?? ''),
        'pacrTime2' => ($_POST['pacrTime2'] ?? ''),
        'pacrTime3' => ($_POST['pacrTime3'] ?? ''),
        'pacrHR1' => $_POST['pacrHR1'] ?? '',
        'pacrHR2' => $_POST['pacrHR2'] ?? '',
        'pacrHR3' => $_POST['pacrHR3'] ?? '',
        'pacrBP1' => $_POST['pacrBP1'] ?? '',
        'pacrBP2' => $_POST['pacrBP2'] ?? '',
        'pacrBP3' => $_POST['pacrBP3'] ?? '',
        'pacrRR1' => $_POST['pacrRR1'] ?? '',
        'pacrRR2' => $_POST['pacrRR2'] ?? '',
        'pacrRR3' => $_POST['pacrRR3'] ?? '',
        'pacrTemp1' => $_POST['pacrTemp1'] ?? '',
        'pacrTemp2' => $_POST['pacrTemp2'] ?? '',
        'pacrTemp3' => $_POST['pacrTemp3'] ?? '',
        'pacrSpO21' => $_POST['pacrSpO21'] ?? '',
        'pacrSpO22' => $_POST['pacrSpO22'] ?? '',
        'pacrSpO23' => $_POST['pacrSpO23'] ?? '',
        'pacrFIO21' => $_POST['pacrFIO21'] ?? '',
        'pacrFIO22' => $_POST['pacrFIO22'] ?? '',
        'pacrFIO23' => $_POST['pacrFIO23'] ?? '',
        'pacrInerpret' => $_POST['pacrInerpret'] ?? '',
        'pacrMapFormula' => $_POST['pacrMapFormula'] ?? '',
        'pacrMap1BP' => $_POST['pacrMap1BP'] ?? '',
        'pacrMap2BP' => $_POST['pacrMap2BP'] ?? '',
        'pacrMap3BP' => $_POST['pacrMap3BP'] ?? '',
        'visi1Check' => $_POST['visi1Check'] ?? 0,
        'visi2Check' => $_POST['visi2Check'] ?? 0,
        'visi3Check' => $_POST['visi3Check'] ?? 0,
        'pacrMapElevated' => $_POST['pacrMapElevated'] ?? '',
        'pacrMapLow' => $_POST['pacrMapLow'] ?? '',

        'pacrCBGDate' => ($pacrCBGDate ?? ''),
        'pacrNa' => $_POST['pacrNa'] ?? '',
        'pacrK' => $_POST['pacrK'] ?? '',
        'pacrCl' => $_POST['pacrCl'] ?? '',
        'pacrCa' => $_POST['pacrCa'] ?? '',
        'pacrBun' => $_POST['pacrBun'] ?? '',
        'pacrGlucose' => $_POST['pacrGlucose'] ?? '',
        'pacrPtt' => $_POST['pacrPtt'] ?? '',
        'pacrAlbumin' => $_POST['pacrAlbumin'] ?? '',
        'pacrSensitivity' => $_POST['pacrSensitivity'] ?? '',
        'pacrAfb' => $_POST['pacrAfb'] ?? '',
        'pacrCreatine' => $_POST['pacrCreatine'] ?? '',
        'pacrRbc' => $_POST['pacrRbc'] ?? '',
        'pacrHb' => $_POST['pacrHb'] ?? '',
        'pacrHct' => $_POST['pacrHct'] ?? '',
        'pacrWbc' => $_POST['pacrWbc'] ?? '',
        'pacrBaso' => $_POST['pacrBaso'] ?? '',
        'pacrLymph' => $_POST['pacrLymph'] ?? '',
        'pacrEosin' => $_POST['pacrEosin'] ?? '',
        'pacrBnp' => $_POST['pacrBnp'] ?? '',
        'pacrTropinin' => $_POST['pacrTropinin'] ?? '',
        'pacrLactate' => $_POST['pacrLactate'] ?? '',
        'pacrIntake' => $_POST['pacrIntake'] ?? '',
        'pacrTotalCO2' => $_POST['pacrTotalCO2'] ?? '',
        'pacrPh' => $_POST['pacrPh'] ?? '',
        'pacrPaCO2' => $_POST['pacrPaCO2'] ?? '',
        'pacrPaO2' => $_POST['pacrPaO2'] ?? '',
        'pacrHCO3' => $_POST['pacrHCO3'] ?? '',
        'pacrBE' => $_POST['pacrBE'] ?? '',
        'pacrCoHb' => $_POST['pacrCoHb'] ?? '',
        'pacrSaO2' => $_POST['pacrSaO2'] ?? '',
        'pacrFIO2' => $_POST['pacrFIO2'] ?? '',      
        'pacrInterpretPatient' => $_POST['pacrInterpretPatient'] ?? '',
        'pacrInterpretABGStatus' => $_POST['pacrInterpretABGStatus'] ?? '',
        'pacrAaFormula' => $_POST['pacrAaFormula'] ?? '',
        'pacrAaNormalvalue' => $_POST['pacrAaNormalvalue'] ?? '',
        'pacrAaCheck' => $_POST['pacrAaCheck'] ?? 0,
        'pacrAaAbnormal' => $_POST['pacrAaAbnormal'] ?? '',
        'pacrCaO2' => $_POST['pacrCaO2'] ?? '',
        'pacrNormalCaO2' => $_POST['pacrNormalCaO2'] ?? '',
        'pacrCaO2Check' => $_POST['pacrCaO2Check'] ?? 0,
        'pacrCaO2Abnormal' => $_POST['pacrCaO2Abnormal'] ?? '',
        'pacrProcedures' => $_POST['pacrProcedures'] ?? '',
        'pacrPFTList' => $_POST['pacrPFTList'] ?? '',
        'pacrCXRList' => $_POST['pacrCXRList'] ?? '',
        
        'pacrSvO2' => $_POST['pacrSvO2'] ?? '',
        'pacrCVP' => $_POST['pacrCVP'] ?? '',
        'pacrCO' => $_POST['pacrCO'] ?? '',
        'pacrPCWP' => $_POST['pacrPCWP'] ?? '',
        'pacrPAP' => $_POST['pacrPAP'] ?? '',
        'pacrICP' => $_POST['pacrICP'] ?? '',
        'pacrRespiratoryOrders' => $_POST['pacrRespiratoryOrders'] ?? '',
        'pacrIndications' => $_POST['pacrIndications'] ?? '',
        'pacrGoals' => $_POST['pacrGoals'] ?? '',
        'pacrRTOrder' => $_POST['pacrRTOrder'] ?? '',
        'pacrVentilator' => $_POST['pacrVentilator'] ?? '',
        'pacrABGResults' => $_POST['pacrABGResults'] ?? '',
        'pacrHemodynamicCheckvalues' => $_POST['pacrHemodynamicCheckvalues'] ?? '', // This was empty in original code, assigned from POST
        'pacrSummery' => $_POST['pacrSummery'] ?? '',

        'studentcomments' => $_POST['studentcomments'] ?? '',
    ];

    
    // Merge the keyed medications into the data array
    $data = array_merge($data, $pacrMedicationsKeyed,$pacrClassificationsKeyed,$pacrModeOfctionsKeyed,$pacrDosageKeyed,$pacrCBCCheckValueKeyed);
    // print_r($data);   
    // exit;
}
else if($type == 'floor') {
    // print_r($_POST);
    $userId  = ($_POST['hiddenStudentId']) ?? 0;

    $caseStudydate = date('m/d/Y', strtotime($_POST['caseStudydate']));
    $floorAdmissionDate = date('m/d/Y', strtotime($_POST['floorAdmissionDate']));

    $flooMedicationsUseList = ($_POST['flooMedicationsUseList'] ?? []);
    $floorModificationCarePlanList = ($_POST['floorModificationCarePlanList'] ?? []);
    
    $floorModificationCarePlanListKeyed = [];
    $cnt1 = 0;
    foreach ($floorModificationCarePlanList as $idx => $val) {
        // Always assign, even if empty, so floorModificationCarePlanList1 is always set
        $floorModificationCarePlanListKeyed['floorModificationCarePlan' . ($cnt1 + 1)] = is_array($val) ? ($val[0] ?? '') : $val;
        $cnt1 ++;
    } 

    $flooMedicationsUseListKeyed = [];   
    $cnt5 = 0;
    foreach ($flooMedicationsUseList as $idx => $val) {
        // Always assign, even if empty, so floorModificationCarePlanList1 is always set
        $flooMedicationsUseListKeyed['flooMedicationsUse' . ($cnt5 + 1)] = is_array($val) ? ($val[0] ?? '') : $val;
        $cnt5 ++;
    }


    $data = [
        'hiddenCaseStudyId' => ($_POST['hiddenCaseStudyId'] ?? 0),
        'caseStudydate' => ($caseStudydate ?? ''),
        'cborotation' => ($_POST['cborotation'] ?? ''),
        'hospitalId' => $_POST['cbohospitalsites'] ?? 0,
        'cbohospitalsiteunits' => $_POST['cbohospitalsiteunits'] ?? 0,
        'floorCohort' => $_POST['floorCohort'] ?? '',
        'floorAdmissionDate' => ($floorAdmissionDate ?? ''),
        'floorPtAge' => $_POST['floorPtAge'] ?? '',
        'floorSex' => $_POST['floorSex'] ?? '',
        'floorHt' => $_POST['floorHt'] ?? '',
        'floorSmoking' => $_POST['floorSmoking'] ?? '',
        'floorChiefComplaint' => $_POST['floorChiefComplaint'] ?? '',
        'floorRespiratory' => $_POST['floorRespiratory'] ?? '',
        'floorPastMedicalHx' => $_POST['floorPastMedicalHx'] ?? '',
        'floorOtherPulmonaryProblems' => ($_POST['floorOtherPulmonaryProblems'] ?? ''),

        'floorHR' => ($_POST['floorHR'] ?? ''),
        'floorSpontRR' => ($_POST['floorSpontRR'] ?? ''),
        'floorBP' => $_POST['floorBP'] ?? '',
        'floorTemp' => $_POST['floorTemp'] ?? '',
        'floorSpO2' => $_POST['floorSpO2'] ?? '',
        'floorIO' => $_POST['floorIO'] ?? '',
        'floorBreathSounds' => $_POST['floorBreathSounds'] ?? '',
        'floorLevelofActivity' => $_POST['floorLevelofActivity'] ?? '',

        'floorNa' => $_POST['floorNa'] ?? '',
        'floorK' => $_POST['floorK'] ?? '',
        'floorCl' => $_POST['floorCl'] ?? '',
        'floorWBC' => $_POST['floorWBC'] ?? '',
        'floorHgb' => $_POST['floorHgb'] ?? '',
        'floorHct' => $_POST['floorHct'] ?? '',
        'floorCO2' => $_POST['floorCO2'] ?? '',
        'floorBUN' => $_POST['floorBUN'] ?? '',
        'floorGlucose' => $_POST['floorGlucose'] ?? '',
        'floorPlatelets' => $_POST['floorPlatelets'] ?? '',
        'floorINR' => $_POST['floorINR'] ?? '',
        'floorSputumCult' => $_POST['floorSputumCult'] ?? '',
        'floorCreatinine' => $_POST['floorCreatinine'] ?? '',
        'floorLabInterpretation' => $_POST['floorLabInterpretation'] ?? '',
        'floorXRayInterpretation' => $_POST['floorXRayInterpretation'] ?? '',
        'floorEKG' => $_POST['floorEKG'] ?? '',
        'floorTrachSize' => $_POST['floorTrachSize'] ?? '',
        'floorTrachType' => $_POST['floorTrachType'] ?? 0,
        'floorCuffPressure' => $_POST['floorCuffPressure'] ?? 0,
        'floorpH' => $_POST['floorpH'] ?? 0,
        'floorPaCO2' => $_POST['floorPaCO2'] ?? '',
        'floorHCO3' => $_POST['floorHCO3'] ?? '',

        'floorFVC' => $_POST['floorFVC'] ?? '',
        'floorFEF25' => $_POST['floorFEF25'] ?? '',
        'floorFEF1' => $_POST['floorFEF1'] ?? '',
        'floorPaO2' => $_POST['floorPaO2'] ?? '',
        'floorSaO2' => $_POST['floorSaO2'] ?? '',
        'floorFiO2' => $_POST['floorFiO2'] ?? '',
        'floorPEFR' => $_POST['floorPEFR'] ?? '',
        'floorFEV1' => $_POST['floorFEV1'] ?? '',
        'floorDateBloodGas' => $_POST['floorDateBloodGas'] ?? '',
        'floorLungValues' => $_POST['floorLungValues'] ?? '',
        'floorInterpretationABG' => $_POST['floorInterpretationABG'] ?? '',
        'floorInterpretationPFT' => $_POST['floorInterpretationPFT'] ?? '',
        'floorInterpretationPAO2' => $_POST['floorInterpretationPAO2'] ?? '',
        'floorInterpretationAO2' => $_POST['floorInterpretationAO2'] ?? '',
        'floorInterpretationCaO2' => $_POST['floorInterpretationCaO2'] ?? '',
        'floorInterpretationPFRatio' => $_POST['floorInterpretationPFRatio'] ?? '',
        'floorIPAP' => $_POST['floorIPAP'] ?? '',
        'floorEPAP' => $_POST['floorEPAP'] ?? '',
        'floorRate' => $_POST['floorRate'] ?? '',
        'floorFiO2Setting' => $_POST['floorFiO2Setting'] ?? '',
        'floorItime' => $_POST['floorItime'] ?? '',
        'floorRise' => $_POST['floorRise'] ?? '',
        'floorRamp' => $_POST['floorRamp'] ?? '',
        'floorHumidityTemp' => $_POST['floorHumidityTemp'] ?? '',
        'floorSuction' => $_POST['floorSuction'] ?? '',
        'floorCough' => $_POST['floorCough'] ?? '',
        'floorSputumAmount' => $_POST['floorSputumAmount'] ?? '',

        'studentcomments' => $_POST['studentcomments'] ?? '',
    ];

    
    // Merge the keyed medications into the data array
    $data = array_merge($data, $floorModificationCarePlanListKeyed,$flooMedicationsUseListKeyed);
    // print_r($data);   
    // exit;
}
else if($type == 'adult') {
    // print_r($_POST);
    $userId  = ($_POST['hiddenStudentId']) ?? 0;

    $caseStudydate = date('m/d/Y', strtotime($_POST['caseStudydate']));
    $adultAdmissionDate = date('m/d/Y', strtotime($_POST['adultAdmissionDate']));
    $studentDOB = date('m/d/Y', strtotime($_POST['studentDOB']));

    $adultMedicationsUseList = ($_POST['adultMedicationsUseList'] ?? []);
    $adultModificationCarePlanList = ($_POST['adultModificationCarePlanList'] ?? []);
    
    

    $adultMedicationsUseListKeyed = [];   
    $cnt5 = 0;
    foreach ($adultMedicationsUseList as $idx => $val) {
        // Always assign, even if empty, so adultModificationCarePlanList1 is always set
        $adultMedicationsUseListKeyed['adultMedicationsUse' . ($cnt5 + 1)] = is_array($val) ? ($val[0] ?? '') : $val;
        $cnt5 ++;
    }

    $adultModificationCarePlanListKeyed = [];
    $cnt1 = 0;
    foreach ($adultModificationCarePlanList as $idx => $val) {
        // Always assign, even if empty, so adultModificationCarePlanList1 is always set
        $adultModificationCarePlanListKeyed['adultModificationCarePlan' . ($cnt1 + 1)] = is_array($val) ? ($val[0] ?? '') : $val;
        $cnt1 ++;
    }


    $data = [
        'hiddenCaseStudyId' => ($_POST['hiddenCaseStudyId'] ?? 0),
        'caseStudydate' => ($caseStudydate ?? ''),
        'cborotation' => ($_POST['cborotation'] ?? ''),
        'hospitalId' => $_POST['cbohospitalsites'] ?? 0,
        'cbohospitalsiteunits' => $_POST['cbohospitalsiteunits'] ?? 0,
        'adultCohort' => $_POST['adultCohort'] ?? '',
        'adultAdmissionDate' => ($adultAdmissionDate ?? ''),
        'adultPtAge' => $_POST['adultPtAge'] ?? '',
        'adultSex' => $_POST['adultSex'] ?? '',
        'adultHt' => $_POST['adultHt'] ?? '',
        'adultSmoking' => $_POST['adultSmoking'] ?? '',
        'gestationalAOB' => $_POST['gestationalAOB'] ?? '',
        'studentDOB' => ($studentDOB?? ''),
        'adultAPGAR' => $_POST['adultAPGAR'] ?? '',
        'adultRespiratory' => ($_POST['adultRespiratory'] ?? ''),

        'adultChiefComplaint' => ($_POST['adultChiefComplaint'] ?? ''),
        'adultPastMedicalHx' => ($_POST['adultPastMedicalHx'] ?? ''),
        'adultMomsPARA' => $_POST['adultMomsPARA'] ?? '',
        'adultParentSmokingHx' => $_POST['adultParentSmokingHx'] ?? '',
        'adultOtherPulmonaryProblems' => $_POST['adultOtherPulmonaryProblems'] ?? '',

        'adultHR' => $_POST['adultHR'] ?? '',
        'adultSpontRR' => $_POST['adultSpontRR'] ?? '',
        'adultBP' => $_POST['adultBP'] ?? '',

        'adultTemp' => $_POST['adultTemp'] ?? '',
        'adultSpO2' => $_POST['adultSpO2'] ?? '',
        'adultIO' => $_POST['adultIO'] ?? '',
        'adultBreathSounds' => $_POST['adultBreathSounds'] ?? '',
        'adultLevelofActivity' => $_POST['adultLevelofActivity'] ?? '',

        'adultNa' => $_POST['adultNa'] ?? '',
        'adultK' => $_POST['adultK'] ?? '',
        'adultCl' => $_POST['adultCl'] ?? '',
        'adultWBC' => $_POST['adultWBC'] ?? '',
        'adultHgb' => $_POST['adultHgb'] ?? '',
        'adultHct' => $_POST['adultHct'] ?? '',
        'adultCO2' => $_POST['adultCO2'] ?? '',
        'adultBUN' => $_POST['adultBUN'] ?? '',
        'adultGlucose' => $_POST['adultGlucose'] ?? '',
        'adultPlatelets' => $_POST['adultPlatelets'] ?? '',
        'adultINR' => $_POST['adultINR'] ?? '',
        'adultSputumCult' => $_POST['adultSputumCult'] ?? '',
        'adultCreatinine' => $_POST['adultCreatinine'] ?? 0,
        'adultLabInterpretation' => $_POST['adultLabInterpretation'] ?? 0,
        'adultXRayInterpretation' => $_POST['adultXRayInterpretation'] ?? 0,
        'adultETT' => $_POST['adultETT'] ?? '',
        'adultPosition' => $_POST['adultPosition'] ?? '',

        'adultTrachType' => $_POST['adultTrachType'] ?? '',
        'adult1' => $_POST['adult1'] ?? '',
        'adultCuffPressure' => $_POST['adultCuffPressure'] ?? '',
        'adultpH' => $_POST['adultpH'] ?? '',
        'adultPaCO2' => $_POST['adultPaCO2'] ?? '',
        'adultHCO3' => $_POST['adultHCO3'] ?? '',
        'adultSvO2' => $_POST['adultSvO2'] ?? '',
        'adultCO' => $_POST['adultCO'] ?? '',
        'adultPAP' => $_POST['adultPAP'] ?? '',
        'adultPaO2' => $_POST['adultPaO2'] ?? '',
        'adultSaO2' => $_POST['adultSaO2'] ?? '',
        'adultFiO2Lpm' => $_POST['adultFiO2Lpm'] ?? '',
        'adultCVP' => $_POST['adultCVP'] ?? '',
        'adultPCWP' => $_POST['adultPCWP'] ?? '',
        'adultICP' => $_POST['adultICP'] ?? '',
        'adultDateBloodGas' => $_POST['adultDateBloodGas'] ?? '',
        'adultInterpretationHemodynamics' => $_POST['adultInterpretationHemodynamics'] ?? '',
        'adultInterpretationABG' => $_POST['adultInterpretationABG'] ?? '',
        'adultEKGResults' => $_POST['adultEKGResults'] ?? '',
        'adultInterpretationPAO2' => $_POST['adultInterpretationPAO2'] ?? '',
        'adultInterpretationAO2' => $_POST['adultInterpretationAO2'] ?? '',
        'adultInterpretationCaO2' => $_POST['adultInterpretationCaO2'] ?? '',
        'adultInterpretationPFRatio' => $_POST['adultInterpretationPFRatio'] ?? '',
        'adultVentilator' => $_POST['adultVentilator'] ?? '',
        'adultFiO2' => $_POST['adultFiO2'] ?? '',
        'adultPiP' => $_POST['adultPiP'] ?? '',
        'adultPlat' => $_POST['adultPlat'] ?? '',

        'adultRR' => $_POST['adultRR'] ?? '',
        'adultMode' => $_POST['adultMode'] ?? '',
        'adultPSupport' => $_POST['adultPSupport'] ?? '',
        'adultMAP' => $_POST['adultMAP'] ?? '',
        'adultVE' => $_POST['adultVE'] ?? '',
        'adultSetRate' => $_POST['adultSetRate'] ?? '',
        'adultMaxFlow' => $_POST['adultMaxFlow'] ?? '',
        'adultSpontVt' => $_POST['adultSpontVt'] ?? '',
        'adultIE' => $_POST['adultIE'] ?? '',
        'adultSetVt' => $_POST['adultSetVt'] ?? '',
        'adultFlowSens' => $_POST['adultFlowSens'] ?? '',

        'adultCsta' => $_POST['adultCsta'] ?? '',
        'adultRaw' => $_POST['adultRaw'] ?? '',
        'adultVte' => $_POST['adultVte'] ?? '',
        'adultIBWVt' => $_POST['adultIBWVt'] ?? '',
        'adultItime' => $_POST['adultItime'] ?? '',
        'adultPEEPCPAP' => $_POST['adultPEEPCPAP'] ?? '',
        'adultHumidityTemp' => $_POST['adultHumidityTemp'] ?? '',
        'adultSputumAmount' => $_POST['adultSputumAmount'] ?? '',
        'adultOtherSettings' => $_POST['adultOtherSettings'] ?? '',
        'adultCough' => $_POST['adultCough'] ?? '',
        'adultSuction' => $_POST['adultSuction'] ?? '',
        'adultRecommendations' => $_POST['adultRecommendations'] ?? '',
        'adultLowHiPiP' => $_POST['adultLowHiPiP'] ?? '',
        'adultLowHiVte' => $_POST['adultLowHiVte'] ?? '',
        'adultLowHiVe' => $_POST['adultLowHiVe'] ?? '',
        'adultLowHiRR' => $_POST['adultLowHiRR'] ?? '',
        'adultApneaAlert' => $_POST['adultApneaAlert'] ?? '',
        'adultOtherAlarms' => $_POST['adultOtherAlarms'] ?? '',

        'adultIPAP' => $_POST['adultIPAP'] ?? '',
        
        'adultEPAP' => $_POST['adultEPAP'] ?? '',
        'adultRate' => $_POST['adultRate'] ?? '',
        'adultFiO21' => $_POST['adultFiO21'] ?? '',
        'adultItimeSetting' => $_POST['adultItimeSetting'] ?? '',
        'adultRise' => $_POST['adultRise'] ?? '',
        'adultRamp' => $_POST['adultRamp'] ?? '',
        'adultFVC' => $_POST['adultFVC'] ?? '',
        'adultFEF25' => $_POST['adultFEF25'] ?? '',
        'adultFEV1' => $_POST['adultFEV1'] ?? '',
        'adultPEFR' => $_POST['adultPEFR'] ?? '',
        'adultFEV1FVC' => $_POST['adultFEV1FVC'] ?? '',
        'adultLungVolumes' => $_POST['adultLungVolumes'] ?? '',
        'adultInterpretationPFT' => $_POST['adultInterpretationPFT'] ?? '',


        'studentcomments' => $_POST['studentcomments'] ?? '',
    ];

    
    // Merge the keyed medications into the data array
    $data = array_merge($data, $adultModificationCarePlanListKeyed,$adultMedicationsUseListKeyed);
    // print_r($data);   
    // exit;
}
else if($type == 'activitysheet') {
    // print_r($_POST);
    // exit;
    $userId  =  $_POST["hiddenStudentId"] ?? 0;

    $activitysheetdate = isset($_POST['activitysheetdate']) ? date('m/d/Y', strtotime($_POST['activitysheetdate'])): '';
    $schoolDate =  isset($_POST['SchoolDate']) ? date('m/d/Y', strtotime($_POST['SchoolDate'])) : '';
    // $studentDOB = date('m/d/Y', strtotime($_POST['studentDOB']));

    $procedurecodes = isset($_POST['procedurecodes']) ? $_POST['procedurecodes'] : [];
    $newArray = [];
    if (!empty($procedurecodes)) {
        foreach ($procedurecodes as $code => $json) {
            // If value is a JSON string, decode it
            if (is_string($json)) {
                $values = json_decode($json, true);
            } else {
                $values = $json;
            }
            if (is_array($values)) {
                foreach ($values as $key => $value) {
                    $newKey = "{$key}-{$code}";
                    $newArray[$newKey] = $value;
                }
            }
        }
    }

    


    $data = [
        'userId' => ($userId),
        'currentSchoolId' => ($_POST['currentSchoolId'] ?? 0),
        'activitysheetdate' => $activitysheetdate,
        'cborotation' => ($_POST['cborotation'] ?? ''),
        'HospitalSite' => $_POST['HospitalSite'] ?? 0,
        'cbohospitalsiteunits' => $_POST['cbohospitalsiteunits'] ?? 0,
        'isSendToPreceptor' => $_POST['isSendToPreceptor'] ?? 0,
        'preceptorNo' => $_POST['preceptorNo'] ?? '',
        'cboClinicalinstructor' => $_POST['cboClinicalinstructor'] ?? '',
        'student_journal_entry' => $_POST['student_journal_entry'] ?? '',
        'cbodrhospitalsiteunits' => $_POST['cbodrhospitalsiteunits'] ?? '',
        'cbophysician' => $_POST['cbophysician'] ?? '',
        'txtTimeSpent' => $_POST['txtTimeSpent'] ?? '',
        'pointsAwarded'  => ($_POST['txtPointsAwarded'] ?? ''),
        'Student_Response' => $_POST['Student_Response'] ?? '',
        'school_comments' => ($_POST['school_comments'] ?? ''),
        'SchoolDate' => $schoolDate,
    ];

    
    // Merge the keyed medications into the data array
    $data = array_merge($data, $newArray);
    // print_r($data);   
    // exit;
 
}



$uploaddir = "../upload/schools/" . $currentSchoolId . "/student/" . $userId . "/autosave/".$type.'/';  

// echo $uploaddir;exit;
 // Create the directory recursively if it doesn't exist
if (!file_exists($uploaddir)) {
    mkdir($uploaddir, 0777, true);
}
// Save draft and output result
$result = saveDraftFile($uploaddir, $userId, $hiddendraftId, $draftLimit,$data);
echo json_encode($result);
?>
